{"version": 3, "sources": ["style.scss", "_typography.scss", "style.css", "_variables.scss", "_mixins.scss", "_buttons.scss", "_common.scss", "templates/_navigation.scss", "templates/_slider.scss", "templates/_homepage.scss", "templates/_otherspage.scss"], "names": [], "mappings": "AAAA;;;;;qEAKqE;AAErE;;qEAEqE;ACTrE,iBAAiB;AACjB,mFAAY;AAEZ;EACE,2BAA0B;EAC1B,qDAAoD;EACpD,oBAAmB;EACnB,mBAAkB;CCSnB;;ADND;EACE,iBAAgB;EAChB,mCEEkC;EFDlC,oCAAmC;EACnC,gBAAe;EACf,eEZkB;CFanB;;AAED;EACE,iBAAgB;EAChB,eEjBkB;EFkBlB,gBAAe;EACf,iBAAgB;EAChB,mCETkC;CFUnC;;AAED;EACE,eEtBuB;EFuBvB,2BEb4B;EFc5B,iBAAgB;EAChB,iBAAgB;CACjB;;AAED;EACE,gBAAe;CAIhB;;AGhCC;EH2BF;IAGI,gBAAe;GAElB;CCWA;;ADTD;EACE,gBAAe;CAIhB;;AGvCC;EHkCF;IAGI,gBAAe;GAElB;CCcA;;ADZD;EACE,gBAAe;CAIhB;;AG9CC;EHyCF;IAGI,gBAAe;GAElB;CCiBA;;ADfD;EACE,gBAAe;CAIhB;;AGrDC;EHgDF;IAGI,gBAAe;GAElB;CCoBA;;ADlBD;EACE,gBAAe;CAIhB;;AG5DC;EHuDF;IAGI,gBAAe;GAElB;CCuBA;;ADrBD;EACE,gBAAe;CAIhB;;AGnEC;EH8DF;IAGI,gBAAe;GAElB;CC0BA;;AGnGD,kBAAkB;AAClB;EACE,gBAAe;EACf,mCFWkC;EEVlC,2BAA0B;EAC1B,mBAAkB;EAClB,iBAAgB;EAChB,iBAAgB;EAChB,UAAS;EACT,mBAAkB;EAClB,WAAU;EACV,qBAAoB;EACpB,iBAAgB;CA+BjB;;AA1CD;EAcI,mBAAkB;EAClB,YAAW;EACX,YAAW;EACX,YAAW;EACX,QAAO;EACP,YAAW;EACX,YAAW;EACX,8CAAqC;EAArC,sCAAqC;EAArC,yEAAqC;EACrC,8BAAqB;UAArB,sBAAqB;EACrB,6BAAoB;UAApB,qBAAoB;CACrB;;AAxBH;EA2BI,WAAU;EACV,4BAA2B;CAC5B;;AA7BH;EAgCI,iBAAgB;CACjB;;AAjCH;EAsCM,6BAAoB;UAApB,qBAAoB;EACpB,iCAAwB;UAAxB,yBAAwB;CACzB;;AAIL;EACE,gBAAe;EACf,mBAAkB;CACnB;;AAED;EACE,gBAAe;EACf,kBAAiB;CAClB;;AAED;EACE,oBFvDqB;EEwDrB,YFhDU;CEmEX;;AArBD;EAKI,iBFnDQ;CEoDT;;AANH;EASI,+BAAqC;EACrC,eFhEmB;CEqEpB;;AAfH;EAaM,YAAW;CACZ;;AAdL;EAkBI,oBAAuD;EACvD,eFzEmB;CE0EpB;;AAGH;;;EAGE,YFxEU;EEyEV,0BAA6D;EAC7D,sBAAyD;CAC1D;;AAED;EACE,iBF9EU;EE+EV,eFvFqB;EEwFrB,uBFhFU;CEiGX;;AApBD;EAMI,oBF3FmB;CE4FpB;;AAPH;EAUI,oBF/FmB;EEgGnB,YFxFQ;EEyFR,uBFzFQ;CE0FT;;AAbH;EAgBI,iBF7FQ;EE8FR,YF9FQ;EE+FR,uBF/FQ;CEgGT;;AAGH;EACE,0BF5GqB;EE6GrB,eF7GqB;EE8GrB,wBAAuB;CAexB;;AAlBD;EAMI,iBFzGQ;CE0GT;;AAPH;EAUI,oBFrHmB;EEsHnB,eFtHmB;CEuHpB;;AAZH;EAeI,oBF1HmB;EE2HnB,YFnHQ;CEoHT;;AC7HH;EACE,uBHKe;EGJf,mBAAkB;CACnB;;AAED;EACE,oBAAwD;EACxD,YHEU;CGDX;;AAHD;EACE,oBAAwD;EACxD,YHEU;CGDX;;AAED,eAAe;AAEf;EACE,gBAAe;EACf,OAAM;EACN,QAAO;EACP,SAAQ;EACR,UAAS;EACT,uBAAsB;EACtB,aAAY;EACZ,cAAa;EACb,oBAAmB;EACnB,wBAAuB;CACxB;;AAED;;EAEE,sBAAqB;EACrB,YAAW;CACZ;;AAED;EACE,uBAAsB;EACtB,UAAS;CACV;;AACD;EACE,eHnCqB;CGoCtB;;AACD;;;EAGE,sBAAqB;CACtB;;AAED;;;EAGE,gBAAe;EACf,qBAAoB;CAKrB;;AATD;;;EAOI,WAAU;CACX;;AAGH;EACE,eHvDqB;CGwDtB;;AAED;EACE,0BAA+B;CAChC;;AAED;EACE,0BAA+B;CAChC;;AAED;EACE,qBAAoB;CACrB;;AACD;EAEI,eHvEmB;CGwEpB;;AAGH;EACE,WAAU;CACX;;AAED;EACE,kBAAiB;EACjB,qBAAoB;CAUrB;;AARC;EACE,kBAAiB;EACjB,qBAAoB;CACrB;;AAED;EACE,oBAAmB;CACpB;;AAGH;EACE,uBAAsB;EACtB,mCAAkC;EAClC,6BAA4B;CAC7B;;AAED;EACE,iCAAsC;CACvC;;AAED,aAAa;AAEb;EACE,mBAAkB;CAYnB;;AAbD;EAII,mBAAkB;EAClB,YAAW;EACX,aAAY;EACZ,YAAW;EACX,OAAM;EACN,QAAO;EACP,oBHlHqB;EGmHrB,YAAW;CACZ;;AAGH;EACE,sBAAqB;CACtB;;AAED;EACE,0BAAyB;CAC1B;;AAED;EACE,+BAAqC;CACtC;;AAED;EACE,+BAAuC;CACxC;;AAED;EACE,oBH/HY;CGgIb;;AAED;EACE,0BAAgC;CACjC;;AAED;EACE,eH/IkB;CGgJnB;;AAED;EACE,0BAAmC;CACpC;;AAED;EACE,0BAAyB;CAC1B;;AAED;EACE,0BAAyB;CAC1B;;AAED;EACE,0BAAkC;CACnC;;AAED;EACE,2BHvJ4B;CGwJ7B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,+BAA8B;CAC/B;;AAED;EACE,gCAA+B;CAChC;;AAED;EACE,oBAAmB;CACpB;;AAED;EACE,WAAU;CACX;;AAED;EACE,iBAAgB;CACjB;;AAED;EACE,uBAAsB;CACvB;;AAED;EACE,gBAAe;CAChB;;AAED,gBAAgB;AAChB;EACE,sBAAqB;CACtB;;AAED;EAGM,mBAAkB;EAClB,mBAAkB;CAYnB;;AAhBL;EAOQ,mBAAkB;EAClB,uBHlOa;EGmOb,iBAAgB;EAChB,gBAAe;EACf,SAAQ;EACR,WAAU;EACV,YH9OI;EG+OJ,oCAA2B;UAA3B,4BAA2B;CAC5B;;AAKP,iBAAiB;AAEjB;EACE,mBAAkB;CAenB;;AAhBD;EAGI,mBAAkB;EAClB,oBAAmB;CAWpB;;AAfH;EAMM,mBAAkB;EAClB,YAAW;EACX,aAAY;EACZ,YAAW;EACX,mBAAkB;EAClB,oBH1QiB;EG2QjB,YAAW;EACX,SAAQ;CACT;;AAIL;EACE,cAAa;EACb,cAAa;CACd;;AAED;EACE,cAAa;CACd;;ACzRD;EACE,gBAAe;EACf,uCAA8B;EAA9B,+BAA8B;EAA9B,2DAA8B;EAC9B,8BAAqB;UAArB,sBAAqB;EACrB,iBAAgB;CAMjB;;AAVD;EAOI,6BAAoB;UAApB,qBAAoB;EACpB,8BAAqB;UAArB,sBAAqB;CACtB;;AHOD;EGJF;IAEI,4BAA2B;IAC3B,oBJbqB;GIexB;CL2fA;;AKzfD;EACE,0EAAgF;EAChF,qBAAoB;CAKrB;;AHpBC;EGaF;IAKI,0EAAgF;GAEnF;CL6fA;;AK3fD;EACE,mBAAkB;EAClB,oBJ7BqB;CIkCtB;;AHnBC;EGYF;IAKI,gBAAe;GAElB;CL+fA;;AK7fD;EACE,0BJpCuB;EIqCvB,kBAAiB;CAClB;;AAED;EAME,eAAc;EACd,mBAAkB;CAsBnB;;AA7BD;EAEI,0BAAyB;EACzB,iBAAgB;CACjB;;AAJH;EAUI,mBAAkB;EAClB,QAAO;EACP,UAAS;EACT,YAAW;EACX,YAAW;EACX,YAAW;EACX,iBJjDQ;EIkDR,6BAAoB;UAApB,qBAAoB;EACpB,8BAAqB;UAArB,sBAAqB;EACrB,uCAA8B;EAA9B,+BAA8B;EAA9B,2DAA8B;CAC/B;;AApBH;EAyBM,6BAAoB;UAApB,qBAAoB;EACpB,iCAAwB;UAAxB,yBAAwB;CACzB;;AAIL;EACE,YJjEU;CIkEX;;AAED;;EAEE,YJtEU;CIuEX;;AAED;;;;EAIE,YJ7EU;CI8EX;;AAED;EACE,kBAAiB;CAKlB;;AH/EC;EGyEF;IAII,cAAa;GAEhB;CL8fA;;AK5fD;EACE,gBAAe;EACf,OAAM;EACN,YAAW;EACX,YAAW;EACX,iBJ7FU;EI8FV,gCAA+B;CAChC;;AAED;EAIQ,oBAAmB;EACnB,WAAU;EACV,iCAAwB;UAAxB,yBAAwB;CACzB;;AAPP;EAWM,gDAA+C;EAC/C,iCJrHiB;EIsHjB,cAAa;EACb,UAAS;EACT,iBAAgB;EAChB,eAAc;EACd,mBAAkB;EAClB,qBAAoB;EACpB,WAAU;EACV,oCAA2B;UAA3B,4BAA2B;EAC3B,iBJtHM;CIkJP;;AH3IH;EG0FF;IAwBQ,cAAa;IACb,WAAU;IACV,oBAAmB;IACnB,iCAAwB;YAAxB,yBAAwB;IACxB,gCAAuB;YAAvB,wBAAuB;GAqB1B;CLyeJ;;AK1hBD;EAgCQ,+BAA8B;EAC9B,WAAU;EACV,iCAAwB;UAAxB,yBAAwB;CAKzB;;AHjIL;EG0FF;IAqCU,eAAc;GAEjB;CL+fN;;AKtiBD;EA0CQ,mBAAkB;CAMnB;;AH1IL;EG0FF;IA6CU,oBAAmB;IACnB,eAAc;GAEjB;CLigBN;;AKjjBD;EAoDM,mBAAkB;EAClB,eJ1JmB;EI2JnB,qBAAoB;EACpB,mCJnJ8B;CIiK/B;;AH/JH;EG0FF;IA0DQ,mBAAkB;GAWrB;CLyfJ;;AK9jBD;EA8DQ,oBAAmB;CACpB;;AA/DP;EAkEQ,eJ3Ke;EI4Kf,wBAAuB;CACxB;;AC9KP;EACE,uBAAsB;CACvB;;AAED;;EAII,mBAAkB;EAClB,eAAc;EACd,WAAU;EACV,cAAa;EACb,gCLHQ;EKIR,UAAS;EACT,gBAAe;EACf,2BAA0B;EAC1B,wBAAuB;CASxB;;AArBH;;EAeM,WAAU;CACX;;AAhBL;;EAmBM,eLtBiB;CKuBlB;;AApBL;EAwBI,YAAW;CACZ;;AAzBH;EA4BI,SAAQ;CACT;;AA7BH;EAgCI,mBAAkB;EAClB,QAAO;EACP,eAAc;EACd,gBAAe;CAuBhB;;AA1DH;EAsCM,sBAAqB;EACrB,cAAa;CAkBd;;AAzDL;EA2CU,oBL9Ca;CK+Cd;;AA5CT;EAgDQ,mBAAkB;EAClB,WAAU;EACV,iBAAgB;EAChB,aAAY;EACZ,YAAW;EACX,qCLhDI;EKiDJ,UAAS;EACT,WAAU;CACX;;AC5DP,oBAAoB;AACpB;EACE,gBAAe;EACf,eNFqB;EMGrB,sBAAqB;CACtB;;AAED;EACE,mBAAkB;EAClB,mBAAkB;EAClB,kBAAiB;EACjB,mBAAkB;CAwBnB;;ALTC;EKnBF;IAOI,mBAAkB;GAqBrB;CPguBA;;AE9uBC;EKdF;IAWI,oBAAmB;IACnB,mBAAkB;IAClB,kBAAiB;GAepB;EA5BD;IAgBM,gBAAe;GAChB;CPsvBJ;;AE9vBC;EKTF;IAqBI,cAAa;IACb,cAAa;GAMhB;EA5BD;IAyBM,gBAAe;GAChB;CPuvBJ;;AOnvBD,qBAAqB;AAErB,YAAY;AACZ;EACE,gBAAe;EACf,kBAAiB;CAClB;;AAED;EACE,qBAAoB;CACrB;;AAED;EACE,qBAAoB;CAKrB;;AAND;EAII,oDAAmD;CACpD;;AAGH,aAAa;AAEb,mBAAmB;AACnB;EACE,kBAAiB;CAalB;;AAdD;EAII,mBAAkB;EAClB,SAAQ;EACR,QAAO;EACP,oCAA2B;UAA3B,4BAA2B;CAM5B;;AL9DD;EKiDF;IAUM,UAAS;IACT,yCAAgC;YAAhC,iCAAgC;GAEnC;CPsvBF;;AOnvBD;EACE,sBAAqB;EACrB,aAAY;EACZ,YAAW;EACX,mBAAkB;EAClB,oBNhFqB;EMiFrB,YNzEU;EM0EV,gBAAe;EACf,mBAAkB;CAgDnB;;AAxDD;EAWI,kBAAiB;CAClB;;AAZH;EAeI,mBAAkB;EAClB,YAAW;EACX,UAAS;EACT,SAAQ;EACR,yCAAgC;UAAhC,iCAAgC;EAChC,iBNvFQ;EMwFR,mBAAkB;EAClB,SAAQ;EACR,UAAS;EACT,YAAW;EACX,qBAAoB;EACpB,sBAAqB;CACtB;;AA3BH;EA8BI,mBAAkB;EAClB,YAAW;EACX,YAAW;EACX,WAAU;EACV,yCAAgC;UAAhC,iCAAgC;EAChC,oBN9GmB;EM+GnB,mBAAkB;EAClB,SAAQ;EACR,UAAS;EACT,YAAW;EACX,qBAAoB;CACrB;;AAzCH;EA6CM,YAAW;EACX,WAAU;EACV,qBAAoB;CACrB;;AAhDL;EAmDM,UAAS;EACT,SAAQ;EACR,oBAAmB;CACpB;;AAIL,oBAAoB;AAEpB,YAAY;AACZ;EACE,mBAAkB;EAClB,oBN1IqB;EM2IrB,2BN7H4B;EM8H5B,mBAAkB;EAClB,cAAa;EACb,YNtIU;EMuIV,OAAM;EACN,QAAO;EACP,0BAAyB;CAK1B;;AAdD;EAYI,gBAAe;CAChB;;AAGH,aAAa;AAEb,aAAa;AACb;EACE,WAAU;EACV,UAAS;EACT,SAAQ;CACT;;AAED,cAAc;AAEd,YAAY;AACZ;EACE,0EAAgF;EAChF,sBAAqB;EACrB,mBAAkB;EAClB,WAAU;CASX;;AAPC;EACE,mBAAkB;CAKnB;;AL3KD;EKqKA;IAII,mBAAkB;GAErB;CPmvBF;;AOhvBD;EACE,mBAAkB;CAQnB;;AATD;EAII,mBAAkB;EAClB,YAAW;EACX,SAAQ;EACR,oCAA2B;UAA3B,4BAA2B;CAC5B;;AAGH;EACE,aAAY;EACZ,iBNxLU;EMyLV,iBAAgB;EAChB,mBAAkB;CAMnB;;AAVD;EAOI,sBNrMmB;EMsMnB,iBAAgB;CACjB;;AAGH;EAEI,aAAY;CACb;;AAGH;EACE,0BAAyB;CAC1B;;AAED;EACE,kBAAiB;EACjB,sBAAqB;CACtB;;AAED;EACE,iCAAgC;EAChC,mBAAkB;CACnB;;AAED,aAAa;AC/Nb;EAEI,gBAAe;EACf,qBAAoB;CAQrB;;AAXH;EAKM,iBAAgB;EAChB,ePLiB;COMlB;;AAPL;EASM,ePRiB;COSlB", "file": "../style.css", "sourcesContent": ["/*!------------------------------------------------------------------\n[MAIN STYLESHEET]\n\nPROJECT:\tProject Name\nVERSION:\tVersoin Number\n-------------------------------------------------------------------*/\n\n/*------------------------------------------------------------------\n[TABLE OF CONTENTS]\n-------------------------------------------------------------------*/\n\n@import 'variables';\n\n@import 'mixins';\n\n@import 'typography';\n\n@import 'buttons';\n\n@import 'common';\n\n@import 'templates/navigation';\n\n@import 'templates/slider';\n\n@import 'templates/homepage';\n\n@import 'templates/otherspage';", "/*  typography */\n@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700');\n\n@font-face {\n  font-family: 'futura-bold';\n  src: url('../fonts/Futura-Bold.woff') format('woff');\n  font-weight: normal;\n  font-style: normal;\n}\n\nbody {\n  line-height: 1.2;\n  font-family: $primary-font;\n  -webkit-font-smoothing: antialiased;\n  font-size: 15px;\n  color: $text-color;\n}\n\np, .paragraph {\n  font-weight: 400;\n  color: $text-color;\n  font-size: 15px;\n  line-height: 1.9;\n  font-family: $primary-font;\n}\n\nh1,h2,h3,h4,h5,h6 {\n  color: $text-color-dark;\n  font-family: $secondary-font;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\nh1, .h1{\n  font-size: 60px;\n  @include mobile {\n    font-size: 45px;\n  }\n}\n\nh2, .h2{\n  font-size: 40px;\n  @include mobile {\n    font-size: 30px;\n  }\n}\n\nh3, .h3{\n  font-size: 25px;\n  @include mobile {\n    font-size: 20px;\n  }\n}\n\nh4, .h4{\n  font-size: 20px;\n  @include mobile {\n    font-size: 18px;\n  }\n}\n\nh5, .h5{\n  font-size: 18px;\n  @include mobile {\n    font-size: 16px;\n  }\n}\n\nh6, .h6{\n  font-size: 16px;\n  @include mobile {\n    font-size: 14px;\n  }\n}\n", "/*!------------------------------------------------------------------\n[MAIN STYLESHEET]\n\nPROJECT:\tProject Name\nVERSION:\tVersoin Number\n-------------------------------------------------------------------*/\n/*------------------------------------------------------------------\n[TABLE OF CONTENTS]\n-------------------------------------------------------------------*/\n/*  typography */\n@import url(\"https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700\");\n@font-face {\n  font-family: 'futura-bold';\n  src: url(\"../fonts/Futura-Bold.woff\") format(\"woff\");\n  font-weight: normal;\n  font-style: normal;\n}\n\nbody {\n  line-height: 1.2;\n  font-family: \"Poppins\", sans-serif;\n  -webkit-font-smoothing: antialiased;\n  font-size: 15px;\n  color: #5c5c77;\n}\n\np, .paragraph {\n  font-weight: 400;\n  color: #5c5c77;\n  font-size: 15px;\n  line-height: 1.9;\n  font-family: \"Poppins\", sans-serif;\n}\n\nh1, h2, h3, h4, h5, h6 {\n  color: #1e1e4b;\n  font-family: \"futura-bold\";\n  font-weight: 700;\n  line-height: 1.2;\n}\n\nh1, .h1 {\n  font-size: 60px;\n}\n\n@media (max-width: 575px) {\n  h1, .h1 {\n    font-size: 45px;\n  }\n}\n\nh2, .h2 {\n  font-size: 40px;\n}\n\n@media (max-width: 575px) {\n  h2, .h2 {\n    font-size: 30px;\n  }\n}\n\nh3, .h3 {\n  font-size: 25px;\n}\n\n@media (max-width: 575px) {\n  h3, .h3 {\n    font-size: 20px;\n  }\n}\n\nh4, .h4 {\n  font-size: 20px;\n}\n\n@media (max-width: 575px) {\n  h4, .h4 {\n    font-size: 18px;\n  }\n}\n\nh5, .h5 {\n  font-size: 18px;\n}\n\n@media (max-width: 575px) {\n  h5, .h5 {\n    font-size: 16px;\n  }\n}\n\nh6, .h6 {\n  font-size: 16px;\n}\n\n@media (max-width: 575px) {\n  h6, .h6 {\n    font-size: 14px;\n  }\n}\n\n/* Button style */\n.btn {\n  font-size: 16px;\n  font-family: \"Poppins\", sans-serif;\n  text-transform: capitalize;\n  padding: 15px 40px;\n  border-radius: 0;\n  font-weight: 500;\n  border: 0;\n  position: relative;\n  z-index: 1;\n  transition: .2s ease;\n  overflow: hidden;\n}\n\n.btn::before {\n  position: absolute;\n  content: \"\";\n  height: 80%;\n  width: 100%;\n  left: 0;\n  bottom: 10%;\n  z-index: -1;\n  transition: transform .2s ease-in-out;\n  transform-origin: top;\n  transform: scaleY(0);\n}\n\n.btn:focus {\n  outline: 0;\n  box-shadow: none !important;\n}\n\n.btn:active {\n  box-shadow: none;\n}\n\n.btn:hover::before {\n  transform: scaleY(1);\n  transform-origin: bottom;\n}\n\n.btn-sm {\n  font-size: 14px;\n  padding: 10px 35px;\n}\n\n.btn-xs {\n  font-size: 12px;\n  padding: 5px 15px;\n}\n\n.btn-primary {\n  background: #ffbc3b;\n  color: #fff;\n}\n\n.btn-primary::before {\n  background: #fff;\n}\n\n.btn-primary:active {\n  background: #ffbc3b !important;\n  color: #ffbc3b;\n}\n\n.btn-primary:active::before {\n  height: 80%;\n}\n\n.btn-primary:hover {\n  background: #ffab08;\n  color: #ffbc3b;\n}\n\n.btn-primary:not(:disabled):not(.disabled).active,\n.btn-primary:not(:disabled):not(.disabled):active,\n.show > .btn-primary.dropdown-toggle {\n  color: #fff;\n  background-color: #ffab08;\n  border-color: #ffab08;\n}\n\n.btn-secondary {\n  background: #fff;\n  color: #ffbc3b;\n  border: 1px solid #fff;\n}\n\n.btn-secondary::before {\n  background: #ffbc3b;\n}\n\n.btn-secondary:active {\n  background: #ffbc3b;\n  color: #fff;\n  border: 1px solid #fff;\n}\n\n.btn-secondary:hover {\n  background: #fff;\n  color: #fff;\n  border: 1px solid #fff;\n}\n\n.btn-primary-outline {\n  border: 1px solid #ffbc3b;\n  color: #ffbc3b;\n  background: transparent;\n}\n\n.btn-primary-outline::before {\n  background: #fff;\n}\n\n.btn-primary-outline:hover {\n  background: #ffbc3b;\n  color: #ffbc3b;\n}\n\n.btn-primary-outline:active {\n  background: #ffbc3b;\n  color: #fff;\n}\n\nbody {\n  background-color: #fff;\n  overflow-x: hidden;\n}\n\n::selection {\n  background: #ffcd6e;\n  color: #fff;\n}\n\n/* preloader */\n.preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\nol,\nul {\n  list-style-type: none;\n  margin: 0px;\n}\n\nimg {\n  vertical-align: middle;\n  border: 0;\n}\n\na {\n  color: #ffbc3b;\n}\n\na,\na:hover,\na:focus {\n  text-decoration: none;\n}\n\na,\nbutton,\nselect {\n  cursor: pointer;\n  transition: .2s ease;\n}\n\na:focus,\nbutton:focus,\nselect:focus {\n  outline: 0;\n}\n\na:hover {\n  color: #ffbc3b;\n}\n\na.text-primary:hover {\n  color: #ffbc3b !important;\n}\n\na.text-light:hover {\n  color: #ffbc3b !important;\n}\n\nh4 {\n  transition: .2s ease;\n}\n\na h4:hover {\n  color: #ffbc3b;\n}\n\n.slick-slide {\n  outline: 0;\n}\n\n.section {\n  padding-top: 90px;\n  padding-bottom: 90px;\n}\n\n.section-sm {\n  padding-top: 40px;\n  padding-bottom: 40px;\n}\n\n.section-title {\n  margin-bottom: 30px;\n}\n\n.bg-cover {\n  background-size: cover;\n  background-position: center center;\n  background-repeat: no-repeat;\n}\n\n.border-primary {\n  border-color: #ededf1 !important;\n}\n\n/* overlay */\n.overlay {\n  position: relative;\n}\n\n.overlay::before {\n  position: absolute;\n  content: '';\n  height: 100%;\n  width: 100%;\n  top: 0;\n  left: 0;\n  background: #1a1a37;\n  opacity: .8;\n}\n\n.outline-0 {\n  outline: 0 !important;\n}\n\n.d-unset {\n  display: unset !important;\n}\n\n.bg-primary {\n  background: #ffbc3b !important;\n}\n\n.bg-secondary {\n  background: #1a1a37 !important;\n}\n\n.bg-gray {\n  background: #f8f8f8;\n}\n\n.text-primary {\n  color: #ffbc3b !important;\n}\n\n.text-color {\n  color: #5c5c77;\n}\n\n.text-light {\n  color: #8585a4 !important;\n}\n\n.text-lighten {\n  color: #d6d6e0 !important;\n}\n\n.text-muted {\n  color: #b5b5b7 !important;\n}\n\n.text-dark {\n  color: #1e1e4b !important;\n}\n\n.font-secondary {\n  font-family: \"futura-bold\";\n}\n\n.mb-10 {\n  margin-bottom: 10px !important;\n}\n\n.mb-20 {\n  margin-bottom: 20px !important;\n}\n\n.mb-30 {\n  margin-bottom: 30px !important;\n}\n\n.mb-40 {\n  margin-bottom: 40px !important;\n}\n\n.mb-50 {\n  margin-bottom: 50px !important;\n}\n\n.mb-60 {\n  margin-bottom: 60px !important;\n}\n\n.mb-70 {\n  margin-bottom: 70px !important;\n}\n\n.mb-80 {\n  margin-bottom: 80px !important;\n}\n\n.mb-90 {\n  margin-bottom: 90px !important;\n}\n\n.mb-100 {\n  margin-bottom: 100px !important;\n}\n\n.pl-150 {\n  padding-left: 150px;\n}\n\n.zindex-1 {\n  z-index: 1;\n}\n\n.overflow-hidden {\n  overflow: hidden;\n}\n\n.vertical-align-middle {\n  vertical-align: middle;\n}\n\n.icon-md {\n  font-size: 36px;\n}\n\n/* page title */\n.page-title-section {\n  padding: 200px 0 80px;\n}\n\n.custom-breadcrumb li.nasted {\n  position: relative;\n  padding-left: 25px;\n}\n\n.custom-breadcrumb li.nasted::before {\n  position: absolute;\n  font-family: \"themify\";\n  content: \"\\e649\";\n  font-size: 20px;\n  top: 50%;\n  left: -5px;\n  color: #fff;\n  transform: translateY(-50%);\n}\n\n/* /page title */\n.list-styled {\n  padding-left: 25px;\n}\n\n.list-styled li {\n  position: relative;\n  margin-bottom: 15px;\n}\n\n.list-styled li::before {\n  position: absolute;\n  content: \"\";\n  height: 10px;\n  width: 10px;\n  border-radius: 50%;\n  background: #ffbc3b;\n  left: -25px;\n  top: 5px;\n}\n\ntextarea.form-control {\n  height: 200px;\n  padding: 20px;\n}\n\n#map_canvas {\n  height: 500px;\n}\n\n.top-header {\n  font-size: 12px;\n  transition: transform .2s ease;\n  transform-origin: top;\n  font-weight: 600;\n}\n\n.top-header.hide {\n  transform: scaleY(0);\n  transform-origin: top;\n}\n\n@media (max-width: 991px) {\n  .header {\n    position: static !important;\n    background: #1a1a37;\n  }\n}\n\n.navigation {\n  background-image: linear-gradient(to right, transparent 50%, #ffbc3b 50%);\n  transition: .2s ease;\n}\n\n@media (max-width: 575px) {\n  .navigation {\n    background-image: linear-gradient(to right, transparent 70%, #ffbc3b 30%);\n  }\n}\n\n.navbar-nav {\n  padding-left: 50px;\n  background: #ffbc3b;\n}\n\n@media (max-width: 991px) {\n  .navbar-nav {\n    padding-left: 0;\n  }\n}\n\n.nav-bg {\n  background-color: #1a1a37;\n  margin-top: -46px;\n}\n\n.nav-item {\n  margin: 0 15px;\n  position: relative;\n}\n\n.nav-item .nav-link {\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.nav-item::before {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  height: 6px;\n  width: 100%;\n  content: \"\";\n  background: #fff;\n  transform: scaleY(0);\n  transform-origin: top;\n  transition: transform .3s ease;\n}\n\n.nav-item:hover::before, .nav-item.active::before {\n  transform: scaleY(1);\n  transform-origin: bottom;\n}\n\n.navbar-light .navbar-nav .nav-link {\n  color: #fff;\n}\n\nlink:focus,\n.navbar-light .navbar-nav .nav-link:hover {\n  color: #fff;\n}\n\n.navbar-light .navbar-nav .active > .nav-link,\n.navbar-light .navbar-nav .nav-link.active,\n.navbar-light .navbar-nav .nav-link.show,\n.navbar-light .navbar-nav .show > .nav-link {\n  color: #fff;\n}\n\n.navbar-expand-lg .navbar-nav .nav-link {\n  padding: 40px 0px;\n}\n\n@media (max-width: 991px) {\n  .navbar-expand-lg .navbar-nav .nav-link {\n    padding: 20px;\n  }\n}\n\n.sticky {\n  position: fixed;\n  top: 0;\n  width: 100%;\n  z-index: 10;\n  background: #fff;\n  box-shadow: 0 2px 5px #0000000d;\n}\n\n.navbar .dropdown:hover .dropdown-menu {\n  visibility: visible;\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.navbar .dropdown-menu {\n  box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.12);\n  border-bottom: 5px solid #ffbc3b;\n  padding: 15px;\n  top: 96px;\n  border-radius: 0;\n  display: block;\n  visibility: hidden;\n  transition: .3s ease;\n  opacity: 0;\n  transform: translateY(20px);\n  background: #fff;\n}\n\n@media (max-width: 991px) {\n  .navbar .dropdown-menu {\n    display: none;\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n    transform-origin: unset;\n  }\n}\n\n.navbar .dropdown-menu.view {\n  visibility: visible !important;\n  opacity: 1;\n  transform: translateY(0);\n}\n\n@media (max-width: 991px) {\n  .navbar .dropdown-menu.view {\n    display: block;\n  }\n}\n\n.navbar .dropdown-menu.show {\n  visibility: hidden;\n}\n\n@media (max-width: 991px) {\n  .navbar .dropdown-menu.show {\n    visibility: visible;\n    display: block;\n  }\n}\n\n.navbar .dropdown-item {\n  position: relative;\n  color: #1e1e4b;\n  transition: .2s ease;\n  font-family: \"Poppins\", sans-serif;\n}\n\n@media (max-width: 991px) {\n  .navbar .dropdown-item {\n    text-align: center;\n  }\n}\n\n.navbar .dropdown-item:not(:last-child) {\n  margin-bottom: 10px;\n}\n\n.navbar .dropdown-item:hover {\n  color: #ffbc3b;\n  background: transparent;\n}\n\n.hero-section {\n  padding: 250px 0 290px;\n}\n\n.hero-slider .prevArrow,\n.hero-slider .nextArrow {\n  position: absolute;\n  bottom: -123px;\n  z-index: 9;\n  padding: 15px;\n  color: rgba(255, 255, 255, 0.5);\n  border: 0;\n  font-size: 30px;\n  transition: all linear .2s;\n  background: transparent;\n}\n\n.hero-slider .prevArrow:focus,\n.hero-slider .nextArrow:focus {\n  outline: 0;\n}\n\n.hero-slider .prevArrow:hover,\n.hero-slider .nextArrow:hover {\n  color: #ffbc3b;\n}\n\n.hero-slider .prevArrow {\n  right: 60px;\n}\n\n.hero-slider .nextArrow {\n  right: 0;\n}\n\n.hero-slider .slick-dots {\n  position: absolute;\n  left: 0;\n  bottom: -100px;\n  padding-left: 0;\n}\n\n.hero-slider .slick-dots li {\n  display: inline-block;\n  margin: 0 6px;\n}\n\n.hero-slider .slick-dots li.slick-active button {\n  background: #ffbc3b;\n}\n\n.hero-slider .slick-dots li button {\n  color: transparent;\n  padding: 0;\n  overflow: hidden;\n  height: 10px;\n  width: 10px;\n  background: rgba(255, 255, 255, 0.5);\n  border: 0;\n  outline: 0;\n}\n\n/* banner feature */\n.feature-icon {\n  font-size: 50px;\n  color: #ffbc3b;\n  display: inline-block;\n}\n\n.feature-blocks {\n  margin-top: -100px;\n  padding-left: 70px;\n  padding-top: 80px;\n  padding-right: 30%;\n}\n\n@media (max-width: 1400px) {\n  .feature-blocks {\n    padding-right: 10%;\n  }\n}\n\n@media (max-width: 1200px) {\n  .feature-blocks {\n    padding-right: 50px;\n    padding-left: 50px;\n    padding-top: 30px;\n  }\n  .feature-blocks h3 {\n    font-size: 20px;\n  }\n}\n\n@media (max-width: 991px) {\n  .feature-blocks {\n    margin-top: 0;\n    padding: 50px;\n  }\n  .feature-blocks h3 {\n    font-size: 25px;\n  }\n}\n\n/* /banner feature */\n/* course */\n.card-btn {\n  font-size: 12px;\n  padding: 5px 10px;\n}\n\n.flex-basis-33 {\n  flex-basis: 33.3333%;\n}\n\n.hover-shadow {\n  transition: .3s ease;\n}\n\n.hover-shadow:hover {\n  box-shadow: 0px 4px 25px 0px rgba(27, 39, 71, 0.15);\n}\n\n/* /course */\n/* success story */\n.success-video {\n  min-height: 300px;\n}\n\n.success-video .play-btn {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n}\n\n@media (max-width: 767px) {\n  .success-video .play-btn {\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n}\n\n.play-btn {\n  display: inline-block;\n  height: 80px;\n  width: 80px;\n  border-radius: 50%;\n  background: #ffbc3b;\n  color: #fff;\n  font-size: 20px;\n  text-align: center;\n}\n\n.play-btn i {\n  line-height: 80px;\n}\n\n.play-btn::before {\n  position: absolute;\n  content: \"\";\n  height: 0;\n  width: 0;\n  transform: translate(-50%, -50%);\n  background: #fff;\n  border-radius: 50%;\n  top: 50%;\n  left: 50%;\n  z-index: -2;\n  transition: .3s ease;\n  transition-delay: .2s;\n}\n\n.play-btn::after {\n  position: absolute;\n  content: \"\";\n  height: 80%;\n  width: 80%;\n  transform: translate(-50%, -50%);\n  background: #ffbc3b;\n  border-radius: 50%;\n  top: 50%;\n  left: 50%;\n  z-index: -1;\n  transition: .3s ease;\n}\n\n.play-btn:hover::before {\n  height: 80%;\n  width: 80%;\n  transition-delay: 0s;\n}\n\n.play-btn:hover::after {\n  height: 0;\n  width: 0;\n  transition: 0s ease;\n}\n\n/* /success story */\n/* events */\n.card-date {\n  position: absolute;\n  background: #ffbc3b;\n  font-family: \"futura-bold\";\n  text-align: center;\n  padding: 10px;\n  color: #fff;\n  top: 0;\n  left: 0;\n  text-transform: uppercase;\n}\n\n.card-date span {\n  font-size: 40px;\n}\n\n/* /events */\n/* teacher */\n.teacher-info {\n  width: 70%;\n  bottom: 0;\n  right: 0;\n}\n\n/* /teacher */\n/* footer */\n.newsletter {\n  background-image: linear-gradient(to right, transparent 50%, #ffbc3b 50%);\n  margin-bottom: -100px;\n  position: relative;\n  z-index: 1;\n}\n\n.newsletter-block {\n  padding-left: 50px;\n}\n\n@media (max-width: 575px) {\n  .newsletter-block {\n    padding-left: 15px;\n  }\n}\n\n.input-wrapper {\n  position: relative;\n}\n\n.input-wrapper button {\n  position: absolute;\n  right: 25px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.form-control {\n  height: 60px;\n  background: #fff;\n  border-radius: 0;\n  padding-left: 25px;\n}\n\n.form-control:focus {\n  border-color: #ffbc3b;\n  box-shadow: none;\n}\n\n.newsletter-block .form-control {\n  height: 70px;\n}\n\n.bg-footer {\n  background-color: #182b45;\n}\n\n.logo-footer {\n  margin-top: -20px;\n  display: inline-block;\n}\n\n.footer {\n  border-color: #494a43 !important;\n  padding-top: 200px;\n}\n\n/* /footer */\n.filter-controls li {\n  cursor: pointer;\n  transition: .1s ease;\n}\n\n.filter-controls li.mixitup-control-active {\n  font-weight: 600;\n  color: #ffbc3b;\n}\n\n.filter-controls li:hover {\n  color: #ffbc3b;\n}\n", "// Color Variables\n$primary-color: #ffbc3b;\n$secondary-color: #1a1a37;\n$text-color: #5c5c77;\n$text-color-light: #8585a4;\n$text-color-dark: #1e1e4b;\n$body-color: #fff;\n$border-color: #ededf1;\n$black: #000;\n$white: #fff;\n$light: #f8f9fe;\n$gray: #f8f8f8;\n\n// Font Variables\n$primary-font: 'Poppins', sans-serif;\n$secondary-font: 'futura-bold';\n$icon-font: 'themify';", "@mixin mobile-xs{\n  @media(max-width:370px){\n    @content;\n  }\n}\n@mixin mobile{\n  @media(max-width:575px){\n    @content;\n  }\n}\n@mixin tablet{\n  @media(max-width:767px){\n    @content;\n  }\n}\n@mixin desktop{\n  @media(max-width:991px){\n    @content;\n  }\n}\n@mixin desktop-lg{\n  @media(max-width:1200px){\n    @content;\n  }\n}\n@mixin desktop-xl{\n  @media(max-width:1400px){\n    @content;\n  }\n}\n\n@mixin size($size){\n  width: $size; height: $size;\n}", "/* Button style */\n.btn {\n  font-size: 16px;\n  font-family: $primary-font;\n  text-transform: capitalize;\n  padding: 15px 40px;\n  border-radius: 0;\n  font-weight: 500;\n  border: 0;\n  position: relative;\n  z-index: 1;\n  transition: .2s ease;\n  overflow: hidden;\n\n  &::before {\n    position: absolute;\n    content: \"\";\n    height: 80%;\n    width: 100%;\n    left: 0;\n    bottom: 10%;\n    z-index: -1;\n    transition: transform .2s ease-in-out;\n    transform-origin: top;\n    transform: scaleY(0);\n  }\n\n  &:focus {\n    outline: 0;\n    box-shadow: none !important;\n  }\n\n  &:active {\n    box-shadow: none;\n  }\n\n  &:hover {\n\n    &::before {\n      transform: scaleY(1);\n      transform-origin: bottom;\n    }\n  }\n}\n\n.btn-sm {\n  font-size: 14px;\n  padding: 10px 35px;\n}\n\n.btn-xs {\n  font-size: 12px;\n  padding: 5px 15px;\n}\n\n.btn-primary {\n  background: $primary-color;\n  color: $white;\n\n  &::before {\n    background: $white;\n  }\n\n  &:active {\n    background: $primary-color !important;\n    color: $primary-color;\n\n    &::before {\n      height: 80%;\n    }\n  }\n\n  &:hover {\n    background: darken($color: $primary-color, $amount: 10);\n    color: $primary-color;\n  }\n}\n\n.btn-primary:not(:disabled):not(.disabled).active,\n.btn-primary:not(:disabled):not(.disabled):active,\n.show>.btn-primary.dropdown-toggle {\n  color: $white;\n  background-color: darken($color: $primary-color, $amount: 10);\n  border-color: darken($color: $primary-color, $amount: 10);\n}\n\n.btn-secondary {\n  background: $white;\n  color: $primary-color;\n  border: 1px solid $white;\n\n  &::before {\n    background: $primary-color;\n  }\n\n  &:active {\n    background: $primary-color;\n    color: $white;\n    border: 1px solid $white;\n  }\n\n  &:hover {\n    background: $white;\n    color: $white;\n    border: 1px solid $white;\n  }\n}\n\n.btn-primary-outline {\n  border: 1px solid $primary-color;\n  color: $primary-color;\n  background: transparent;\n\n  &::before {\n    background: $white;\n  }\n\n  &:hover {\n    background: $primary-color;\n    color: $primary-color;\n  }\n\n  &:active {\n    background: $primary-color;\n    color: $white;\n  }\n}", "body {\n  background-color: $body-color;\n  overflow-x: hidden;\n}\n\n::selection {\n  background: lighten($color: $primary-color, $amount: 10);\n  color: $white;\n}\n\n/* preloader */\n\n.preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\nol,\nul {\n  list-style-type: none;\n  margin: 0px;\n}\n\nimg {\n  vertical-align: middle;\n  border: 0;\n}\na {\n  color:$primary-color;\n}\na,\na:hover,\na:focus {\n  text-decoration: none;\n}\n\na,\nbutton,\nselect {\n  cursor: pointer;\n  transition: .2s ease;\n\n  &:focus {\n    outline: 0;\n  }\n}\n\na:hover {\n  color: $primary-color;\n}\n\na.text-primary:hover {\n  color: $primary-color!important;\n}\n\na.text-light:hover {\n  color: $primary-color!important;\n}\n\nh4{\n  transition: .2s ease;\n}\na h4{\n  &:hover{\n    color: $primary-color;\n  }\n}\n\n.slick-slide {\n  outline: 0;\n}\n\n.section {\n  padding-top: 90px;\n  padding-bottom: 90px;\n\n  &-sm {\n    padding-top: 40px;\n    padding-bottom: 40px;\n  }\n\n  &-title {\n    margin-bottom: 30px;\n  }\n}\n\n.bg-cover {\n  background-size: cover;\n  background-position: center center;\n  background-repeat: no-repeat;\n}\n\n.border-primary {\n  border-color: $border-color !important;\n}\n\n/* overlay */\n\n.overlay {\n  position: relative;\n\n  &::before {\n    position: absolute;\n    content: '';\n    height: 100%;\n    width: 100%;\n    top: 0;\n    left: 0;\n    background: $secondary-color;\n    opacity: .8;\n  }\n}\n\n.outline-0 {\n  outline: 0 !important;\n}\n\n.d-unset {\n  display: unset !important;\n}\n\n.bg-primary {\n  background: $primary-color !important;\n}\n\n.bg-secondary {\n  background: $secondary-color !important;\n}\n\n.bg-gray {\n  background: $gray;\n}\n\n.text-primary {\n  color: $primary-color !important;\n}\n\n.text-color {\n  color: $text-color;\n}\n\n.text-light {\n  color: $text-color-light !important;\n}\n\n.text-lighten{\n  color: #d6d6e0 !important;\n}\n\n.text-muted {\n  color: #b5b5b7 !important;\n}\n\n.text-dark {\n  color: $text-color-dark !important;\n}\n\n.font-secondary {\n  font-family: $secondary-font;\n}\n\n.mb-10 {\n  margin-bottom: 10px !important;\n}\n\n.mb-20 {\n  margin-bottom: 20px !important;\n}\n\n.mb-30 {\n  margin-bottom: 30px !important;\n}\n\n.mb-40 {\n  margin-bottom: 40px !important;\n}\n\n.mb-50 {\n  margin-bottom: 50px !important;\n}\n\n.mb-60 {\n  margin-bottom: 60px !important;\n}\n\n.mb-70 {\n  margin-bottom: 70px !important;\n}\n\n.mb-80 {\n  margin-bottom: 80px !important;\n}\n\n.mb-90 {\n  margin-bottom: 90px !important;\n}\n\n.mb-100 {\n  margin-bottom: 100px !important;\n}\n\n.pl-150 {\n  padding-left: 150px;\n}\n\n.zindex-1 {\n  z-index: 1;\n}\n\n.overflow-hidden {\n  overflow: hidden;\n}\n\n.vertical-align-middle{\n  vertical-align: middle;\n}\n\n.icon-md{\n  font-size: 36px;\n}\n\n/* page title */\n.page-title-section {\n  padding: 200px 0 80px;\n}\n\n.custom-breadcrumb {\n  li {\n    &.nasted {\n      position: relative;\n      padding-left: 25px;\n\n      &::before {\n        position: absolute;\n        font-family: $icon-font;\n        content: \"\\e649\";\n        font-size: 20px;\n        top: 50%;\n        left: -5px;\n        color: $white;\n        transform: translateY(-50%);\n      }\n    }\n  }\n}\n\n/* /page title */\n\n.list-styled{\n  padding-left: 25px;\n  li{\n    position: relative;\n    margin-bottom: 15px;\n    &::before{\n      position: absolute;\n      content: \"\";\n      height: 10px;\n      width: 10px;\n      border-radius: 50%;\n      background: $primary-color;\n      left: -25px;\n      top: 5px;\n    }\n  }\n}\n\ntextarea.form-control{\n  height: 200px;\n  padding: 20px;\n}\n\n#map_canvas{\n  height: 500px;\n}", ".top-header {\n  font-size: 12px;\n  transition: transform .2s ease;\n  transform-origin: top;\n  font-weight: 600;\n\n  &.hide {\n    transform: scaleY(0);\n    transform-origin: top;\n  }\n}\n\n.header{\n  @include desktop {\n    position: static !important;\n    background: $secondary-color;\n  }\n}\n\n.navigation {\n  background-image: linear-gradient(to right, transparent 50%, $primary-color 50%);\n  transition: .2s ease;\n\n  @include mobile {\n    background-image: linear-gradient(to right, transparent 70%, $primary-color 30%);\n  }\n}\n\n.navbar-nav {\n  padding-left: 50px;\n  background: $primary-color;\n\n  @include desktop {\n    padding-left: 0;\n  }\n}\n\n.nav-bg {\n  background-color: $secondary-color;\n  margin-top: -46px;\n}\n\n.nav-item {\n  .nav-link {\n    text-transform: uppercase;\n    font-weight: 600;\n  }\n\n  margin: 0 15px;\n  position: relative;\n\n  &::before {\n    position: absolute;\n    left: 0;\n    bottom: 0;\n    height: 6px;\n    width: 100%;\n    content: \"\";\n    background: $white;\n    transform: scaleY(0);\n    transform-origin: top;\n    transition: transform .3s ease;\n  }\n\n  &:hover,\n  &.active {\n    &::before {\n      transform: scaleY(1);\n      transform-origin: bottom;\n    }\n  }\n}\n\n.navbar-light .navbar-nav .nav-link {\n  color: $white;\n}\n\nlink:focus,\n.navbar-light .navbar-nav .nav-link:hover {\n  color: $white;\n}\n\n.navbar-light .navbar-nav .active>.nav-link,\n.navbar-light .navbar-nav .nav-link.active,\n.navbar-light .navbar-nav .nav-link.show,\n.navbar-light .navbar-nav .show>.nav-link {\n  color: $white;\n}\n\n.navbar-expand-lg .navbar-nav .nav-link {\n  padding: 40px 0px;\n\n  @include desktop {\n    padding: 20px;\n  }\n}\n\n.sticky {\n  position: fixed;\n  top: 0;\n  width: 100%;\n  z-index: 10;\n  background: $white;\n  box-shadow: 0 2px 5px #0000000d;\n}\n\n.navbar {\n  .dropdown {\n    &:hover {\n      .dropdown-menu {\n        visibility: visible;\n        opacity: 1;\n        transform: translateY(0)\n      }\n    }\n\n    &-menu {\n      box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.12);\n      border-bottom: 5px solid $primary-color;\n      padding: 15px;\n      top: 96px;\n      border-radius: 0;\n      display: block;\n      visibility: hidden;\n      transition: .3s ease;\n      opacity: 0;\n      transform: translateY(20px);\n      background: $white;\n\n      @include desktop {\n        display: none;\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n        transform-origin: unset;\n      }\n\n      &.view {\n        visibility: visible !important;\n        opacity: 1;\n        transform: translateY(0);\n\n        @include desktop {\n          display: block;\n        }\n      }\n\n      &.show {\n        visibility: hidden;\n\n        @include desktop {\n          visibility: visible;\n          display: block;\n        }\n      }\n    }\n\n    &-item {\n      position: relative;\n      color: $text-color-dark;\n      transition: .2s ease;\n      font-family: $primary-font;\n\n      @include desktop {\n        text-align: center;\n      }\n\n      &:not(:last-child) {\n        margin-bottom: 10px;\n      }\n\n      &:hover {\n        color: $primary-color;\n        background: transparent;\n      }\n    }\n  }\n}", ".hero-section {\n  padding: 250px 0 290px;\n}\n\n.hero-slider {\n\n  .prevArrow,\n  .nextArrow {\n    position: absolute;\n    bottom: -123px;\n    z-index: 9;\n    padding: 15px;\n    color: rgba($color: $white, $alpha: .5);\n    border: 0;\n    font-size: 30px;\n    transition: all linear .2s;\n    background: transparent;\n\n    &:focus {\n      outline: 0;\n    }\n\n    &:hover {\n      color: $primary-color;\n    }\n  }\n\n  .prevArrow {\n    right: 60px;\n  }\n\n  .nextArrow {\n    right: 0;\n  }\n\n  .slick-dots {\n    position: absolute;\n    left: 0;\n    bottom: -100px;\n    padding-left: 0;\n\n    li {\n      display: inline-block;\n      margin: 0 6px;\n\n      &.slick-active {\n        button {\n          background: $primary-color;\n        }\n      }\n\n      button {\n        color: transparent;\n        padding: 0;\n        overflow: hidden;\n        height: 10px;\n        width: 10px;\n        background: rgba($color: $white, $alpha: .5);\n        border: 0;\n        outline: 0;\n      }\n    }\n  }\n}", "/* banner feature */\n.feature-icon {\n  font-size: 50px;\n  color: $primary-color;\n  display: inline-block;\n}\n\n.feature-blocks {\n  margin-top: -100px;\n  padding-left: 70px;\n  padding-top: 80px;\n  padding-right: 30%;\n\n  @include desktop-xl {\n    padding-right: 10%;\n  }\n\n  @include desktop-lg {\n    padding-right: 50px;\n    padding-left: 50px;\n    padding-top: 30px;\n\n    h3 {\n      font-size: 20px;\n    }\n  }\n\n  @include desktop {\n    margin-top: 0;\n    padding: 50px;\n\n    h3 {\n      font-size: 25px;\n    }\n  }\n}\n\n/* /banner feature */\n\n/* course */\n.card-btn {\n  font-size: 12px;\n  padding: 5px 10px;\n}\n\n.flex-basis-33 {\n  flex-basis: 33.3333%;\n}\n\n.hover-shadow {\n  transition: .3s ease;\n\n  &:hover {\n    box-shadow: 0px 4px 25px 0px rgba(27, 39, 71, 0.15);\n  }\n}\n\n/* /course */\n\n/* success story */\n.success-video {\n  min-height: 300px;\n\n  .play-btn {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    transform: translateY(-50%);\n\n    @include tablet {\n      left: 50%;\n      transform: translate(-50%, -50%);\n    }\n  }\n}\n\n.play-btn {\n  display: inline-block;\n  height: 80px;\n  width: 80px;\n  border-radius: 50%;\n  background: $primary-color;\n  color: $white;\n  font-size: 20px;\n  text-align: center;\n\n  i {\n    line-height: 80px;\n  }\n\n  &::before {\n    position: absolute;\n    content: \"\";\n    height: 0;\n    width: 0;\n    transform: translate(-50%, -50%);\n    background: $white;\n    border-radius: 50%;\n    top: 50%;\n    left: 50%;\n    z-index: -2;\n    transition: .3s ease;\n    transition-delay: .2s;\n  }\n\n  &::after {\n    position: absolute;\n    content: \"\";\n    height: 80%;\n    width: 80%;\n    transform: translate(-50%, -50%);\n    background: $primary-color;\n    border-radius: 50%;\n    top: 50%;\n    left: 50%;\n    z-index: -1;\n    transition: .3s ease;\n  }\n\n  &:hover {\n    &::before {\n      height: 80%;\n      width: 80%;\n      transition-delay: 0s;\n    }\n\n    &::after {\n      height: 0;\n      width: 0;\n      transition: 0s ease;\n    }\n  }\n}\n\n/* /success story */\n\n/* events */\n.card-date {\n  position: absolute;\n  background: $primary-color;\n  font-family: $secondary-font;\n  text-align: center;\n  padding: 10px;\n  color: $white;\n  top: 0;\n  left: 0;\n  text-transform: uppercase;\n\n  span {\n    font-size: 40px;\n  }\n}\n\n/* /events */\n\n/* teacher */\n.teacher-info {\n  width: 70%;\n  bottom: 0;\n  right: 0;\n}\n\n/* /teacher */\n\n/* footer */\n.newsletter {\n  background-image: linear-gradient(to right, transparent 50%, $primary-color 50%);\n  margin-bottom: -100px;\n  position: relative;\n  z-index: 1;\n\n  &-block {\n    padding-left: 50px;\n\n    @include mobile {\n      padding-left: 15px;\n    }\n  }\n}\n\n.input-wrapper {\n  position: relative;\n\n  button {\n    position: absolute;\n    right: 25px;\n    top: 50%;\n    transform: translateY(-50%);\n  }\n}\n\n.form-control {\n  height: 60px;\n  background: $white;\n  border-radius: 0;\n  padding-left: 25px;\n\n  &:focus {\n    border-color: $primary-color;\n    box-shadow: none;\n  }\n}\n\n.newsletter-block {\n  .form-control {\n    height: 70px;\n  }\n}\n\n.bg-footer {\n  background-color: #182b45;\n}\n\n.logo-footer {\n  margin-top: -20px;\n  display: inline-block;\n}\n\n.footer {\n  border-color: #494a43 !important;\n  padding-top: 200px;\n}\n\n/* /footer */", ".filter-controls{\n  li{\n    cursor: pointer;\n    transition: .1s ease;\n    &.mixitup-control-active{\n      font-weight: 600;\n      color: $primary-color;\n    }\n    &:hover{\n      color: $primary-color;\n    }\n  }\n}"]}