# Educenter - Education Website

Dự án website giáo dục được chuyển đổi từ HTML tĩnh sang Express.js với EJS template engine.

## Cấu trúc dự án

```
educenter-master/
├── views/                  # Các file EJS templates
│   ├── partials/          # Các component dùng chung
│   │   ├── head.ejs       # HTML head section
│   │   ├── header.ejs     # Header navigation
│   │   ├── footer.ejs     # Footer
│   │   ├── scripts.ejs    # JavaScript files
│   │   └── modals.ejs     # Login/Register modals
│   ├── index.ejs          # Trang chủ
│   ├── about.ejs          # Trang giới thiệu
│   ├── courses.ejs        # Trang khóa học
│   ├── course-single.ejs  # Chi tiết khóa học
│   ├── events.ejs         # Trang sự kiện
│   ├── event-single.ejs   # Chi tiết sự kiện
│   ├── blog.ejs           # Trang blog
│   ├── blog-single.ejs    # Chi tiết bài viết
│   ├── teacher.ejs        # Trang giáo viên
│   ├── teacher-single.ejs # Chi tiết gi<PERSON>o viên
│   ├── notice.ejs         # Trang thông báo
│   ├── notice-single.ejs  # Chi tiết thông báo
│   ├── research.ejs       # Trang nghiên cứu
│   ├── scholarship.ejs    # Trang học bổng
│   └── contact.ejs        # Trang liên hệ
├── public/                # Static files
│   ├── css/              # CSS files
│   ├── js/               # JavaScript files
│   ├── images/           # Images
│   └── plugins/          # Third-party plugins
├── server.js             # Express server
├── package.json          # Dependencies
└── README.md            # Hướng dẫn này
```

## Cài đặt và chạy

1. **Cài đặt dependencies:**
   ```bash
   npm install
   ```

2. **Chạy server:**
   ```bash
   npm start
   ```

3. **Chạy server với nodemon (development):**
   ```bash
   npm run dev
   ```

4. **Truy cập website:**
   Mở trình duyệt và truy cập: `http://localhost:3000`

## Các trang có sẵn

- `/` - Trang chủ
- `/about` - Giới thiệu
- `/courses` - Khóa học
- `/course-single` - Chi tiết khóa học
- `/events` - Sự kiện
- `/event-single` - Chi tiết sự kiện
- `/blog` - Blog
- `/blog-single` - Chi tiết bài viết
- `/teacher` - Giáo viên
- `/teacher-single` - Chi tiết giáo viên
- `/notice` - Thông báo
- `/notice-single` - Chi tiết thông báo
- `/research` - Nghiên cứu
- `/scholarship` - Học bổng
- `/contact` - Liên hệ

## Tính năng

- ✅ Responsive design
- ✅ EJS template engine với layout chung
- ✅ Static file serving
- ✅ Navigation với active state
- ✅ Modular component structure
- ✅ SEO-friendly URLs

## Công nghệ sử dụng

- **Backend:** Node.js, Express.js
- **Template Engine:** EJS
- **Frontend:** Bootstrap 4, jQuery
- **Icons:** Themify Icons
- **Animations:** AOS (Animate On Scroll)

## Ghi chú

- Tất cả đường dẫn đã được cập nhật để hoạt động với Express.js
- Static files được serve từ thư mục `public/`
- Layout chung được sử dụng để tránh lặp code
- Navigation tự động highlight trang hiện tại
